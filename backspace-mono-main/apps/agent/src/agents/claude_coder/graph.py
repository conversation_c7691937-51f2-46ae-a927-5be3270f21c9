"""LangGraph implementation for Claude-Coder agent."""

import logging
import os
import subprocess
from typing import Any, Dict, Optional

from agents.base import BaseGraph
from agents.claude_coder.states import ClaudeCoderState
from agents.claude_coder.prompts import (
    get_modularize_prompts,
    get_build_prompts,
    get_test_prompts,
    get_doc_prompts
)
from agents.claude_e2b import create_sandbox, cleanup_sandbox, run_claude_in_sandbox
from langgraph.graph import END, START
from langgraph.graph.state import CompiledGraph, StateGraph
from langchain_core.runnables.config import RunnableConfig

logger = logging.getLogger(__name__)


class ClaudeCoderGraph(BaseGraph):
    """Graph for automated development pipeline."""

    def __init__(self):
        """Initialize Claude-Coder graph."""
        super().__init__()
    
    # =================================== HELPER METHODS ====================================
    
    async def _get_current_branch(self) -> str:
        """Get the current git branch name."""
        try:
            import asyncio
            proc = await asyncio.create_subprocess_exec(
                "git", "branch", "--show-current",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await proc.communicate()
            
            if proc.returncode == 0:
                return stdout.decode().strip()
            else:
                logger.error(f"Failed to get current git branch: {stderr.decode()}")
                return "main"
        except Exception as e:
            logger.error(f"Failed to get current git branch: {e}")
            return "main"
    
    async def _get_repo_path(self) -> str:
        """Get the repository root path."""
        try:
            import asyncio
            proc = await asyncio.create_subprocess_exec(
                "git", "rev-parse", "--show-toplevel",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await proc.communicate()
            
            if proc.returncode == 0:
                return stdout.decode().strip()
            else:
                logger.error(f"Failed to get repository root: {stderr.decode()}")
                return os.getcwd()
        except Exception as e:
            logger.error(f"Failed to get repository root: {e}")
            return os.getcwd()



    # =================================== NODES ====================================
    
    async def create_sandbox_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Create E2B sandbox and initialize git state."""
        logger.info("🚀 Creating E2B sandbox for Claude-Coder session...")
        
        try:
            # Auto-detect git information if not provided
            branch_name = state.get("branch_name") or await self._get_current_branch()
            base_branch = state.get("base_branch", "main")
            repo_path = state.get("repo_path") or await self._get_repo_path()
            
            logger.info(f"📍 Working on branch: {branch_name}")
            logger.info(f"📍 Base branch: {base_branch}")
            logger.info(f"📍 Repository: {repo_path}")
            
            # Create sandbox
            sandbox = await create_sandbox(timeout=3600)
            
            # Fetch latest changes and checkout to the specified branch in sandbox
            checkout_cmd = f"cd /home/<USER>/workspace && git fetch origin && (git checkout -B {branch_name} origin/{branch_name} || git checkout -b {branch_name})"
            result = await sandbox.commands.run(checkout_cmd)
            
            if result.exit_code != 0:
                logger.warning(f"Failed to checkout branch {branch_name}: {result.stderr}")
            
            return {
                "sandbox": sandbox,
                "branch_name": branch_name,
                "base_branch": base_branch,
                "repo_path": repo_path,
                "current_phase": "modularize",
                "phase_results": {},
                "error": None
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to create sandbox: {e}")
            return {
                "error": f"Failed to create sandbox: {str(e)}",
                "current_phase": "error"
            }
    
    async def modularize_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run modularization phase with Claude Code."""
        logger.info("🔧 Starting MODULARIZE phase...")

        try:
            sandbox = state["sandbox"]
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]

            # Get prompts
            system_prompt, user_prompt = get_modularize_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )

            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"

            # Run Claude Code
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                claude_options=state.get("claude_options", {"max-turns": "10"}),
                timeout=3600  # 10 minutes
            )

            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["modularize"] = session

            if not session.success:
                return {
                    "error": f"Modularize phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }

            logger.info("✅ MODULARIZE phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "modularize_complete"
            }

        except Exception as e:
            logger.error(f"❌ Modularize phase error: {e}")
            return {
                "error": f"Modularize phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def build_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run build phase with Claude Code."""
        logger.info("🏗️ Starting BUILD phase...")

        try:
            sandbox = state["sandbox"]
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]

            # Get prompts
            system_prompt, user_prompt = get_build_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )

            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"

            # Run Claude Code
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                claude_options=state.get("claude_options", {"max-turns": "15"}),
                timeout=3600  # 15 minutes
            )

            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["build"] = session

            if not session.success:
                return {
                    "error": f"Build phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }

            logger.info("✅ BUILD phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "build_complete"
            }

        except Exception as e:
            logger.error(f"❌ Build phase error: {e}")
            return {
                "error": f"Build phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def test_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run test phase with Claude Code."""
        logger.info("🧪 Starting TEST phase...")

        try:
            sandbox = state["sandbox"]
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]

            # Get prompts
            system_prompt, user_prompt = get_test_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )

            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"

            # Run Claude Code
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                claude_options=state.get("claude_options", {"max-turns": "20"}),
                timeout=3600  # 20 minutes for testing
            )

            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["test"] = session

            if not session.success:
                return {
                    "error": f"Test phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }

            logger.info("✅ TEST phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "test_complete"
            }

        except Exception as e:
            logger.error(f"❌ Test phase error: {e}")
            return {
                "error": f"Test phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def doc_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run documentation phase with Claude Code and create PR."""
        logger.info("📝 Starting DOC phase...")

        try:
            sandbox = state["sandbox"]
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]

            # Get prompts
            system_prompt, user_prompt = get_doc_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )

            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"

            # Run Claude Code
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                claude_options=state.get("claude_options", {"max-turns": "15"}),
                timeout=3600  # 15 minutes
            )

            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["doc"] = session

            if not session.success:
                return {
                    "error": f"Doc phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }

            # Extract PR URL if created
            pr_url = None
            for output in session.outputs:
                if output.type == "claude_message" and "github.com" in str(output.content) and "/pull/" in str(output.content):
                    # Simple extraction of PR URL
                    content = str(output.content)
                    start = content.find("https://github.com")
                    if start != -1:
                        end = content.find(" ", start)
                        if end == -1:
                            end = len(content)
                        pr_url = content[start:end].strip()
                        break

            logger.info("✅ DOC phase completed successfully")
            if pr_url:
                logger.info(f"📌 PR created/updated: {pr_url}")

            return {
                "phase_results": phase_results,
                "pr_url": pr_url,
                "current_phase": "doc_complete"
            }

        except Exception as e:
            logger.error(f"❌ Doc phase error: {e}")
            return {
                "error": f"Doc phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def cleanup_sandbox_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Clean up E2B sandbox."""
        logger.info("🧹 Cleaning up sandbox...")
        
        sandbox = state.get("sandbox")
        if sandbox:
            try:
                await cleanup_sandbox(sandbox)
                logger.info("✅ Sandbox cleaned up successfully")
            except Exception as e:
                logger.warning(f"⚠️ Error during sandbox cleanup: {e}")
        
        return {
            "sandbox": None,
            "current_phase": "complete"
        }
    
    # =================================== EDGE CONDITIONS ====================================
    
    def should_continue(self, state: ClaudeCoderState) -> str:
        """Determine next step based on current phase."""
        current_phase = state.get("current_phase", "")
        error = state.get("error")
        
        logger.info(f"🔍 should_continue: current_phase='{current_phase}', error='{error}'")
        
        if current_phase == "error" or error:
            logger.info("➡️ Routing to cleanup due to error")
            return "cleanup"
        
        # Map completion states to next phases
        phase_transitions = {
            "modularize_complete": "build",
            "build_complete": "test", 
            "test_complete": "doc",
            "doc_complete": "cleanup",
            "cleanup": "cleanup"  # cleanup goes to END via direct edge
        }
        
        next_phase = phase_transitions.get(current_phase, "cleanup")
        logger.info(f"➡️ Routing from '{current_phase}' to '{next_phase}'")
        return next_phase
    
    # =================================== COMPILE ====================================
    
    def compile(self) -> CompiledGraph:
        """Compile the Claude-Coder graph."""
        builder = StateGraph(ClaudeCoderState)
        
        # Add all nodes
        builder.add_node("create_sandbox", self.create_sandbox_node)
        builder.add_node("modularize", self.modularize_node)
        builder.add_node("build", self.build_node)
        builder.add_node("test", self.test_node)
        builder.add_node("doc", self.doc_node)
        builder.add_node("cleanup", self.cleanup_sandbox_node)
        
        # Define the flow
        builder.add_edge(START, "create_sandbox")
        builder.add_edge("create_sandbox", "modularize")
        
        # Add conditional edges from each phase
        # Each node sets current_phase to the NEXT phase
        # should_continue() returns where to go based on that next phase
        builder.add_conditional_edges(
            "modularize",
            self.should_continue,
            {
                "build": "build",
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "build", 
            self.should_continue,
            {
                "test": "test",
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "test",
            self.should_continue,
            {
                "doc": "doc", 
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "doc",
            self.should_continue,
            {
                "cleanup": "cleanup"
            }
        )
        
        builder.add_edge("cleanup", END)
        
        return builder.compile()