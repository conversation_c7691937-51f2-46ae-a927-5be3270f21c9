"""Claude Code integration utilities for E2B sandbox."""

import json
import logging
import asyncio
import os
from typing import Optional, List, Dict, Any, AsyncIterator, Callable
from dataclasses import dataclass, field
from datetime import datetime

from e2b_code_interpreter import AsyncSandbox

# Configure logger with emoji support
logger = logging.getLogger(__name__)


@dataclass
class ClaudeOutput:
    """Container for storing Claude Code outputs."""
    timestamp: float
    type: str
    content: Any
    raw_event: Optional[Dict[str, Any]] = None
    
    def __str__(self):
        return f"[{self.timestamp:.2f}] {self.type}: {self.content}"


@dataclass
class ClaudeSession:
    """Container for a complete Claude Code session."""
    session_id: str
    prompt: str
    outputs: List[ClaudeOutput] = field(default_factory=list)
    start_time: float = field(default_factory=lambda: datetime.now().timestamp())
    end_time: Optional[float] = None
    total_cost_usd: Optional[float] = None
    duration_ms: Optional[int] = None
    success: bool = False
    error: Optional[str] = None
    
    def add_output(self, output: ClaudeOutput):
        """Add an output to the session."""
        self.outputs.append(output)
    
    def finalize(self, success: bool = True, error: Optional[str] = None):
        """Mark the session as complete."""
        self.end_time = datetime.now().timestamp()
        self.success = success
        self.error = error
        
    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds."""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now().timestamp() - self.start_time


def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) -> Optional[ClaudeOutput]:
    """Handle a single line from Claude's stream output.
    
    Args:
        line: Raw line from Claude's JSON stream
        session: Optional session to store outputs in
        
    Returns:
        ClaudeOutput object if successfully parsed, None otherwise
    """
    if not line.strip():
        return None

    try:
        event = json.loads(line)
        event_type = event.get('type')
        timestamp = datetime.now().timestamp()
        output = None

        if event_type == 'system':
            logger.info("🚀 SYSTEM INIT")
            logger.info(f"   📁 CWD: {event.get('cwd')}")
            logger.info(f"   🤖 Model: {event.get('model')}")
            logger.info(f"   🛡️ Permission Mode: {event.get('permissionMode')}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="system",
                content={
                    "cwd": event.get('cwd'),
                    "model": event.get('model'),
                    "permissionMode": event.get('permissionMode')
                },
                raw_event=event
            )

        elif event_type == 'assistant':
            msg = event.get('message', {})
            content = msg.get('content', [])

            for item in content:
                if item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"🤖 Claude: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=text,
                        raw_event=event
                    )
                    
                elif item.get('type') == 'tool_use':
                    tool = item.get('name')
                    inp = item.get('input', {})
                    
                    tool_emojis = {
                        'Write': '📝',
                        'Bash': '⚡',
                        'Read': '👀',
                        'Edit': '✏️',
                        'MultiEdit': '✂️',
                        'TodoRead': '📋',
                        'TodoWrite': '✅',
                        'WebSearch': '🔍',
                        'WebFetch': '🌐'
                    }
                    
                    emoji = tool_emojis.get(tool, '🔧')
                    
                    if tool == 'Write':
                        logger.info(f"{emoji} Writing: {inp.get('file_path')}")
                    elif tool == 'Bash':
                        logger.info(f"{emoji} Running: {inp.get('command')}")
                    elif tool == 'Read':
                        logger.info(f"{emoji} Reading: {inp.get('file_path')}")
                    elif tool == 'Edit':
                        logger.info(f"{emoji} Editing: {inp.get('file_path')}")
                    else:
                        logger.info(f"{emoji} Tool: {tool}")
                    
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_call",
                        content={
                            "name": tool,
                            "input": inp
                        },
                        raw_event=event
                    )

        elif event_type == 'user':
            msg = event.get('message', {})
            for item in msg.get('content', []):
                if item.get('type') == 'tool_result':
                    tool_use_id = item.get('tool_use_id')
                    is_error = item.get('is_error', False)
                    content = item.get('content', '')
                    
                    if is_error:
                        logger.error(f"   ❌ Error: {content[:100]}...")
                    else:
                        if content:
                            logger.info(f"   ✅ Result: {content[:100]}...")
                        else:
                            logger.info("   ✅ Success")
                    
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={
                            "tool_use_id": tool_use_id,
                            "is_error": is_error,
                            "content": content
                        },
                        raw_event=event
                    )

        elif event_type == 'result':
            is_error = event.get('is_error', False)
            result = event.get('result', '')
            duration_ms = event.get('duration_ms', 0)
            total_cost_usd = event.get('total_cost_usd', 0)
            
            logger.info("🏁 FINAL RESULT:")
            logger.info(f"   📊 {'✅ SUCCESS' if not is_error else '❌ FAILED'}")
            logger.info(f"   📝 {result}")
            logger.info(f"   ⏱️  {duration_ms}ms")
            logger.info(f"   💰 ${total_cost_usd:.4f}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="result",
                content={
                    "is_error": is_error,
                    "result": result,
                    "duration_ms": duration_ms,
                    "total_cost_usd": total_cost_usd
                },
                raw_event=event
            )
            
            # Update session if provided
            if session:
                session.duration_ms = duration_ms
                session.total_cost_usd = total_cost_usd
                session.finalize(success=not is_error, error=result if is_error else None)

        elif event_type == 'error':
            error_msg = event.get('error', event.get('message', 'Unknown error'))
            logger.error(f"💥 Error: {error_msg}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="error",
                content=error_msg,
                raw_event=event
            )
            
            if session:
                session.finalize(success=False, error=error_msg)

        # Add output to session if provided
        if output and session:
            session.add_output(output)
            
        return output

    except json.JSONDecodeError as e:
        logger.debug(f"⚠️ Failed to parse JSON line: {line[:50]}... - {e}")
        return None
    except Exception as e:
        logger.error(f"💥 Unexpected error handling stream: {e}")
        return None


async def run_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    on_output: Optional[Callable[[ClaudeOutput], None]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300
) -> ClaudeSession:
    """Run Claude Code in the E2B sandbox with the given prompt.

    Args:
        sandbox: The E2B sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI (e.g., --model, --max-turns)
        on_output: Optional callback for each output
        cwd: Working directory for Claude
        timeout: Command timeout in seconds

    Returns:
        ClaudeSession object containing all outputs and metadata
    """
    session_id = f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}"
    session = ClaudeSession(session_id=session_id, prompt=prompt)

    logger.info(f"🤖 Starting Claude Code session: {session_id}")
    logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
    
    # Build Claude command
    cmd_parts = ["claude", "-p", "--output-format", "stream-json", "--verbose", "--dangerously-skip-permissions"]
    
    # Add any additional options
    if claude_options:
        for key, value in claude_options.items():
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))
    
    # Add the prompt via echo
    # Escape the prompt for shell
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    full_command = f"echo '{escaped_prompt}' | {' '.join(cmd_parts)}"
    
    logger.debug(f"🔧 Full command: {full_command[:200]}...")
    
    try:
        # Run the command with streaming
        logger.info("⚡ Executing Claude Code...")
        
        async def handle_stdout(data: str):
            """Handle stdout data from the command."""
            for line in data.strip().split('\n'):
                if line:
                    output = handle_claude_stream(line, session)
                    if output and on_output:
                        if asyncio.iscoroutinefunction(on_output):
                            await on_output(output)
                        else:
                            on_output(output)
        
        async def handle_stderr(data: str):
            """Handle stderr data from the command."""
            if data.strip():
                logger.warning(f"⚠️ STDERR: {data}")
        
        # Execute with streaming
        result = await sandbox.commands.run(
            full_command,
            on_stdout=handle_stdout,
            on_stderr=handle_stderr,
            cwd=cwd or "/home/<USER>/workspace",
            timeout=timeout
        )
        
        if result.exit_code != 0:
            error_msg = f"Claude command exited with code {result.exit_code}"
            logger.error(f"❌ {error_msg}")
            session.finalize(success=False, error=error_msg)
        else:
            logger.info(f"✅ Claude Code session completed successfully")
            if not session.end_time:  # If not already finalized by result event
                session.finalize(success=True)
        
    except asyncio.TimeoutError:
        error_msg = f"Claude command timed out after {timeout} seconds"
        logger.error(f"⏱️ {error_msg}")
        session.finalize(success=False, error=error_msg)
    except Exception as e:
        error_msg = f"Failed to run Claude: {e}"
        logger.error(f"💥 {error_msg}")
        session.finalize(success=False, error=error_msg)

    logger.info(f"📊 Session summary:")
    logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
    if session.total_cost_usd:
        logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")

    return session


async def stream_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300
) -> AsyncIterator[ClaudeOutput]:
    """Stream Claude Code outputs as they arrive.
    
    This is a generator version that yields outputs in real-time.
    
    Args:
        sandbox: The E2B sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        
    Yields:
        ClaudeOutput objects as they are parsed from the stream
    """
    outputs_queue = asyncio.Queue()
    session = ClaudeSession(
        session_id=f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}",
        prompt=prompt
    )
    
    async def output_callback(output: ClaudeOutput):
        await outputs_queue.put(output)
    
    # Run Claude in a background task
    async def run_claude_task():
        try:
            await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                on_output=output_callback,
                cwd=cwd,
                timeout=timeout
            )
        finally:
            # Signal completion
            await outputs_queue.put(None)
    
    # Start the Claude task
    task = asyncio.create_task(run_claude_task())
    
    try:
        # Yield outputs as they arrive
        while True:
            output = await outputs_queue.get()
            if output is None:  # End signal
                break
            yield output
    finally:
        # Ensure task is complete
        await task