# 🚀 Backspace - AI-Powered Development Platform

> **Transform your development workflow with intelligent AI agents that analyze, fix, and enhance your code automatically.**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Next.js](https://img.shields.io/badge/Next.js-15+-black.svg)](https://nextjs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)](https://fastapi.tiangolo.com)

<p align="center">
  <a href="#-what-is-backspace"><strong>Introduction</strong></a> |
  <a href="#-key-features"><strong>Features</strong></a> |
  <a href="#-tech-stack"><strong>Tech Stack</strong></a> |
  <a href="#-quick-start"><strong>Quick Start</strong></a> |
  <a href="#-ai-agents"><strong>AI Agents</strong></a>
</p>

## 🎯 What is Backspace?

Backspace is a comprehensive AI development platform that automates code analysis, bug fixing, testing, and documentation through intelligent AI agents. It combines the power of Claude AI with secure sandbox execution environments to provide a seamless development experience.

**🎉 Recent Major Updates:**
- ✅ **Task 1**: Added LangSmith observability to claude-e2b for complete tracing
- ✅ **Task 2**: Replaced Claude Code CLI with Claude API in claude-coder (solves disk space issues)
- 🚀 **Production Ready**: Full observability, error handling, and performance monitoring

## ✨ Key Features

### 🤖 AI Agents
- **Claude E2B**: Sandbox execution with streaming + LangSmith observability
- **Claude Coder**: Multi-phase workflow (modularize→build→test→doc) with Claude API
- **Scanner Agent**: Comprehensive code analysis and security scanning
- **Branch Agent**: Composite workflows combining multiple agents

### 🛡️ Security & Execution
- **E2B Sandboxes**: Secure cloud execution environments
- **Daytona Integration**: Alternative sandbox runtime
- **GitHub Integration**: Automated PR creation and repository management
- **OAuth Authentication**: Secure user authentication

### 📊 Observability & Monitoring
- **LangSmith Integration**: Complete AI operation tracing
- **Real-time Cost Tracking**: Monitor API usage and costs
- **Performance Analytics**: Detailed execution metrics
- **Error Debugging**: Full context error traces

### 🔄 Automation
- **End-to-End Workflows**: From code analysis to GitHub PR
- **Real-time Streaming**: Live updates and interactive execution
- **Background Processing**: Async task queue system
- **Multi-language Support**: Python, JavaScript, TypeScript, Go

## 🛠️ Tech Stack

### Frontend
- **[Next.js 15+](https://nextjs.org/)** – React framework with App Router
- **[Tailwind CSS](https://tailwindcss.com/)** – Utility-first CSS framework
- **[Radix UI](https://www.radix-ui.com/)** – Accessible component primitives
- **[Shadcn/ui](https://ui.shadcn.com/)** – Beautiful component library
- **[TypeScript](https://www.typescriptlang.org/)** – Type-safe JavaScript

### Backend & AI
- **[FastAPI](https://fastapi.tiangolo.com/)** – High-performance Python API
- **[LangChain](https://langchain.com/)** – AI application framework
- **[LangGraph](https://langchain-ai.github.io/langgraph/)** – Multi-agent workflow engine
- **[LangSmith](https://smith.langchain.com/)** – AI observability platform
- **[Claude API](https://anthropic.com/)** – Advanced AI language model

### Infrastructure & Tools
- **[E2B](https://e2b.dev/)** – Secure cloud sandboxes
- **[Supabase](https://supabase.com/)** – PostgreSQL database & auth
- **[GitHub API](https://docs.github.com/en/rest)** – Repository integration
- **[Turborepo](https://turbo.build/repo)** – Monorepo build system
- **[Go](https://golang.org/)** – CLI tool development

## 🚀 Quick Start

### Prerequisites
```bash
# Required
- Python 3.11+
- Node.js 18+
- Git

# API Keys (get free accounts)
- Anthropic API Key (Claude)
- E2B API Key (Sandboxes)
- LangSmith API Key (Observability - optional)
```

### 1. Clone & Setup
```bash
git clone https://github.com/alhridoy/backspace.git
cd backspace
```

### 2. Environment Configuration
```bash
# Copy environment template
cp apps/agent/.env.example apps/agent/.env

# Add your API keys
ANTHROPIC_API_KEY=sk-ant-api03-...
E2B_API_KEY=e2b_...
LANGSMITH_API_KEY=ls_...  # Optional
LANGSMITH_PROJECT=backspace-demo
```

### 3. Install Dependencies
```bash
# Install Python dependencies
cd apps/agent
pip install -r requirements.txt

# Install Node.js dependencies
cd ../web
npm install

# Install CLI dependencies
cd ../../backspace-cli
go mod download
```

### 4. Test the Setup
```bash
# Test Claude API integration
cd apps/agent
python3 simple_claude_test.py

# Test agent architecture
python3 verify_implementation.py
```

## 🤖 AI Agents

### Claude E2B Agent
**Purpose**: Execute Claude Code CLI in secure sandboxes with streaming output

```python
from agents.claude_e2b import run_claude_in_sandbox, stream_claude_in_sandbox

# Execute with full LangSmith tracing
session = await run_claude_in_sandbox(
    sandbox=sandbox,
    prompt="Create a Python script that analyzes this codebase",
    claude_options={"max-turns": "10"}
)
```

**Features**:
- ✅ Secure E2B sandbox execution
- ✅ Real-time streaming output
- ✅ LangSmith observability
- ✅ Cost tracking
- ✅ Error handling with context

### Claude Coder Agent
**Purpose**: Multi-phase development workflow using Claude API

```python
from agents.claude_coder import ClaudeCoderGraph

# Initialize with Claude API
coder = ClaudeCoderGraph(llm=claude_llm)
graph = coder.compile()

# Execute full workflow
result = await graph.ainvoke({
    "branch_name": "feature/auth-fix",
    "base_branch": "main",
    "repo_path": "/path/to/repo"
})
```

**Workflow Phases**:
1. **🔧 Modularize**: Analyze and improve code structure
2. **🏗️ Build**: Ensure code compiles and dependencies work
3. **🧪 Test**: Run comprehensive test suites
4. **📝 Document**: Generate docs and create GitHub PR

**Key Improvements**:
- ✅ Replaced Claude Code CLI with Claude API (no more disk space issues)
- ✅ Direct tool integration with E2B sandboxes
- ✅ Full LangSmith tracing for each phase
- ✅ Better error handling and recovery

### Scanner Agent
**Purpose**: Comprehensive code analysis and security scanning

```python
from agents.scanner import ScannerGraph

# Run security scan
result = await scanner.scan(
    repo_path="/path/to/repo",
    scan_type="security"  # or "comprehensive"
)
```

**Capabilities**:
- 🔍 Security vulnerability detection
- 📊 Code quality metrics
- 🐛 Bug pattern analysis
- 📈 Performance optimization suggestions
- 🔒 Dependency vulnerability scanning

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   🌐 Web App    │    │   💻 CLI Tool   │    │  📱 Landing     │
│   (Next.js)     │    │   (Go + TUI)    │    │   (Next.js)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────────────┘
          │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────┐
                    │     🚀 FastAPI Backend  │
                    │   (Request Orchestration)│
                    └─────────────┬───────────┘
                                 │
                    ┌─────────────▼───────────┐
                    │   🤖 Agent Manager      │
                    │  (Multi-Agent System)   │
                    └─────────────┬───────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼────────┐    ┌─────────▼────────┐    ┌─────────▼────────┐
│  📦 Claude E2B  │    │ 🔧 Claude Coder  │    │ 🔍 Scanner Agent │
│   + LangSmith   │    │   + Claude API   │    │  + Analysis      │
└───────┬────────┘    └─────────┬────────┘    └─────────┬────────┘
        │                       │                        │
        └───────────────────────┼────────────────────────┘
                               │
                    ┌──────────▼──────────┐
                    │  ☁️ Execution Layer  │
                    │ E2B + Daytona + Tools│
                    └─────────────────────┘
```

## 📊 Observability

### LangSmith Integration
Every AI operation is automatically traced:

```python
@traceable(name="claude_coder_workflow", metadata={"version": "2.0"})
async def run_workflow():
    # All operations automatically traced
    # - API calls and responses
    # - Tool executions
    # - Costs and performance
    # - Error context
```

**What you get**:
- 💰 **Real-time cost tracking** for all AI operations
- ⏱️ **Performance monitoring** with detailed timing
- 🐛 **Error debugging** with full conversation context
- 📈 **Usage analytics** and optimization insights
- 🔍 **Tool execution traces** in sandbox environments

### Monitoring Dashboard
Access your LangSmith dashboard to see:
- Live execution traces
- Cost breakdowns by agent/operation
- Performance bottlenecks
- Error patterns and debugging info

## 🔧 Development

### Project Structure
```
backspace/
├── apps/
│   ├── agent/          # 🤖 AI Agent System
│   │   ├── src/agents/
│   │   │   ├── claude_e2b/     # Sandbox + streaming
│   │   │   ├── claude_coder/   # Multi-phase workflow
│   │   │   ├── scanner/        # Code analysis
│   │   │   └── tools/          # Shared tooling
│   │   └── tests/              # Comprehensive tests
│   ├── api/            # 🚀 FastAPI Backend
│   ├── web/            # 🌐 Next.js Frontend
│   ├── docs/           # 📚 Documentation
│   └── landing/        # 📱 Marketing Site
├── backspace-cli/      # 💻 Go CLI Tool
└── README.md          # 📖 This file
```

### Running Tests
```bash
# Test AI agents
cd apps/agent
python -m pytest tests/ -v

# Test API
cd apps/api
python -m pytest tests/ -v

# Test frontend
cd apps/web
npm test
```

### Local Development
```bash
# Start all services
npm run dev

# Or start individually:
cd apps/web && npm run dev      # Frontend: http://localhost:3000
cd apps/api && python run.py   # API: http://localhost:8000
cd apps/agent && python webapp.py  # Agent UI: http://localhost:8501
```

## 🎯 Use Cases

### 1. Automated Code Review
```bash
# Scan repository for issues
./backspace-cli/bin/backspace.js scan

# Fix issues automatically
./backspace-cli/bin/backspace.js fix --branch feature/security-fixes
```

### 2. Development Workflow
```python
# Complete development cycle
result = await claude_coder.run(
    query="Add user authentication with JWT tokens",
    branch_name="feature/auth"
)
# → Analyzes requirements
# → Implements code changes
# → Runs tests
# → Creates documentation
# → Opens GitHub PR
```

### 3. Code Analysis
```python
# Comprehensive analysis
scan_result = await scanner.analyze(
    repo_path="./my-project",
    focus_areas=["security", "performance", "maintainability"]
)
```

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### 1. Development Setup
```bash
# Fork and clone
git clone https://github.com/your-username/backspace.git
cd backspace

# Create feature branch
git checkout -b feature/amazing-feature

# Set up development environment
./scripts/setup-dev.sh
```

### 2. Making Changes
- 🧪 **Add tests** for new features
- 📝 **Update documentation**
- 🎨 **Follow code style** (ESLint, Prettier, Black)
- ✅ **Ensure all tests pass**

### 3. Submitting
```bash
# Run full test suite
npm run test:all

# Commit with conventional commits
git commit -m "feat: add new agent capability"

# Push and create PR
git push origin feature/amazing-feature
```

### Areas for Contribution
- 🤖 **New AI Agents**: Specialized agents for different domains
- 🔧 **Tool Development**: New tools for sandbox environments
- 🎨 **UI/UX Improvements**: Better user experience
- 📊 **Analytics**: Enhanced monitoring and insights
- 🔒 **Security**: Additional security features
- 📚 **Documentation**: Tutorials and guides

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[Anthropic](https://anthropic.com/)** for Claude AI
- **[E2B](https://e2b.dev/)** for secure sandbox environments
- **[LangChain](https://langchain.com/)** for AI application framework
- **[Vercel](https://vercel.com/)** for deployment platform
- **[Supabase](https://supabase.com/)** for database and auth

---

<p align="center">
  <strong>Built with ❤️ by the Backspace team</strong>
</p>

<p align="center">
  <a href="https://github.com/alhridoy/backspace/issues">Report Bug</a> •
  <a href="https://github.com/alhridoy/backspace/issues">Request Feature</a> •
  <a href="#-quick-start">Get Started</a>
</p>

## License

Next-Fast-Turbo is open-source under the GNU General Public License Version 3 (GPLv3) or any later version.
